
function getAppName() {
  // Safely access environment variable with fallback
  const appName = (typeof process !== 'undefined' && process.env) ? process.env.NEXT_PUBLIC_PROJECT_NAME : undefined;
  if (!appName) {
    // Return default value instead of throwing during build
    return 'ShipAny';
  }
  return appName;
}

function getAppId() {
  // Safely access environment variable with fallback
  const appId = (typeof process !== 'undefined' && process.env) ? process.env.NEXT_PUBLIC_APP_ID : undefined;
  if (!appId) {
    // Return default value instead of throwing during build
    return 'shipany-default';
  }
  return appId.trim();
}

export const app = {
  name: getAppName(),
  id: getAppId(),
}
