import { allCaseStudies } from 'contentlayer/generated'
import { getTranslations, setRequestLocale } from 'next-intl/server'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Link } from '@/i18n/navigation'
import Image from 'next/image'
import { Metadata } from 'next'

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale } = await params
  setRequestLocale(locale)

  const t = await getTranslations()

  const webUrl = process?.env?.NEXT_PUBLIC_WEB_URL || '';
  let canonicalUrl = webUrl ? `${webUrl}/case-studies` : '';

  if (locale !== 'en' && webUrl) {
    canonicalUrl = `${webUrl}/${locale}/case-studies`;
  }

  return {
    title: t('case_studies.title') || 'Case Studies',
    description: t('case_studies.description') || 'Explore our case studies',
    alternates: {
      canonical: canonicalUrl,
    },
  }
}

export default async function CaseStudiesPage({
  params,
}: {
  params: Promise<{ locale: string }>
}) {
  const { locale } = await params
  setRequestLocale(locale)

  const t = await getTranslations()

  // Filter case studies by locale
  const caseStudies = allCaseStudies.filter(caseStudy => caseStudy.lang === locale)

  return (
    <section className="py-16">
      <div className="container">
        <div className="mb-12 text-center">
          <h1 className="mb-4 text-4xl font-bold tracking-tight">
            {t('case_studies.title') || 'Case Studies'}
          </h1>
          <p className="text-xl text-muted-foreground">
            {t('case_studies.description') || 'Explore our case studies'}
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {caseStudies.map((caseStudy) => (
            <Link key={caseStudy.slug} href={`/case-studies/${caseStudy.slug}`}>
              <Card className="h-full transition-colors hover:bg-muted/50">
                {caseStudy.coverImage && (
                  <div className="aspect-video overflow-hidden rounded-t-lg">
                    <Image
                      src={caseStudy.coverImage}
                      alt={caseStudy.title}
                      width={400}
                      height={225}
                      className="h-full w-full object-cover"
                    />
                  </div>
                )}
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <CardTitle className="line-clamp-2">{caseStudy.title}</CardTitle>
                    {caseStudy.featured && (
                      <Badge variant="secondary">Featured</Badge>
                    )}
                  </div>
                  {caseStudy.description && (
                    <CardDescription className="line-clamp-3">
                      {caseStudy.description}
                    </CardDescription>
                  )}
                </CardHeader>
                <CardContent>
                  {caseStudy.tags && caseStudy.tags.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {caseStudy.tags.map((tag) => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>

        {caseStudies.length === 0 && (
          <div className="text-center py-12">
            <p className="text-muted-foreground">
              {t('case_studies.no_case_studies') || 'No case studies found.'}
            </p>
          </div>
        )}
      </div>
    </section>
  )
}
