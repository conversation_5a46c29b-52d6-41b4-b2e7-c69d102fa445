/**
 * Platform Detection Module
 * 
 * This module provides utilities to detect the current deployment platform and runtime environment.
 * It enables writing platform-agnostic code that can adapt to different deployment targets
 * while optimizing bundle size and runtime performance.
 * 
 * Supported Platforms:
 * - 'cloudflare': Cloudflare Workers, Vercel Edge Runtime, or any Edge-compatible environment
 * - 'node': Node.js runtime (includes Vercel Serverless, traditional servers)
 * 
 * Detection Priority (highest to lowest):
 * 1. DEPLOYMENT_TARGET environment variable (explicit override)
 *    - Set to 'cloudflare' for Edge Runtime environments
 *    - Set to 'node' for Node.js environments
 * 2. Runtime characteristics auto-detection
 *    - Checks for Cloudflare Workers globals (caches, no navigator)
 *    - Checks for Vercel Edge Runtime (NEXT_RUNTIME=edge)
 * 3. Defaults to 'node' if unable to determine
 * 
 * Usage Examples:
 * ```typescript
 * import { targetPlatform, isCloudflareEnvironment } from '@/lib/platform';
 * 
 * // Simple platform check
 * if (isCloudflareEnvironment()) {
 *   // Use Edge-compatible APIs
 * }
 * 
 * // Conditional module loading
 * const storage = targetPlatform === 'cloudflare' 
 *   ? await import('./storage-edge')
 *   : await import('./storage-node');
 * ```
 */

/**
 * Supported deployment platforms
 * 
 * @typedef {'cloudflare' | 'node'} TargetPlatform
 * - 'cloudflare': Edge Runtime environments (Cloudflare Workers, Vercel Edge)
 * - 'node': Node.js environments (Vercel Serverless, traditional servers)
 */
export type TargetPlatform = 'cloudflare' | 'node';

/**
 * Internal interface for runtime environment detection results
 * 
 * @interface RuntimeDetection
 * @property {boolean} isCloudflareWorkers - True if running in Cloudflare Workers environment
 * @property {boolean} isVercelEdge - True if running in Vercel Edge Runtime
 * @property {string|undefined} deploymentTarget - Raw DEPLOYMENT_TARGET environment variable value
 */
interface RuntimeDetection {
  isCloudflareWorkers: boolean;
  isVercelEdge: boolean;
  deploymentTarget?: string;
}

/**
 * Detects runtime environment characteristics (internal function)
 * 
 * This function performs the actual environment detection by checking:
 * 1. Environment variables (DEPLOYMENT_TARGET, NEXT_RUNTIME)
 * 2. Global objects availability (caches, navigator, globalThis)
 * 
 * Detection Logic:
 * - Cloudflare Workers: Has `caches` global, no `navigator`, has `globalThis`
 * - Vercel Edge: NEXT_RUNTIME environment variable equals 'edge'
 * 
 * @returns {RuntimeDetection} Object containing detection results
 * @internal
 */
const detectRuntime = (): RuntimeDetection => {
  // Read explicit deployment target from environment
  const deploymentTarget = (typeof process !== 'undefined' && process.env) ? process.env.DEPLOYMENT_TARGET : undefined;

  // Detect Cloudflare Workers environment
  // Cloudflare Workers provide `caches` API but don't have `navigator` (browser-specific)
  const isCloudflareWorkers = (
    typeof caches !== 'undefined' &&
    typeof navigator === 'undefined' &&
    typeof globalThis !== 'undefined'
  );

  // Detect Vercel Edge Runtime environment
  // Next.js sets NEXT_RUNTIME to 'edge' when running in Edge Runtime
  const isVercelEdge = (typeof process !== 'undefined' && process.env) ? process.env.NEXT_RUNTIME === 'edge' : false;

  return {
    isCloudflareWorkers,
    isVercelEdge,
    deploymentTarget,
  };
};

/**
 * Detects the target platform based on environment variables and runtime characteristics
 * 
 * This is the main detection function that determines whether the code is running
 * in an Edge Runtime environment ('cloudflare') or Node.js environment ('node').
 * 
 * Detection Flow:
 * 1. Check DEPLOYMENT_TARGET environment variable for explicit override
 * 2. If not set, auto-detect based on runtime characteristics:
 *    - If Cloudflare Workers or Vercel Edge detected → 'cloudflare'
 *    - Otherwise → 'node' (default)
 * 
 * @returns {TargetPlatform} The detected platform: 'cloudflare' or 'node'
 * 
 * @example
 * ```typescript
 * const platform = getTargetPlatform();
 * if (platform === 'cloudflare') {
 *   // Use Edge-compatible APIs
 * } else {
 *   // Use Node.js APIs
 * }
 * ```
 */
export const getTargetPlatform = (): TargetPlatform => {
  const { 
    isCloudflareWorkers, 
    isVercelEdge, 
    deploymentTarget } = detectRuntime();
  
  // 1. Explicit environment variable (highest priority)
  // Allow override for testing or specific deployment configurations
  if (deploymentTarget === 'cloudflare' || deploymentTarget === 'node') {
    return deploymentTarget;
  }
  
  // 2. Auto-detection based on runtime characteristics
  // If any Edge Runtime environment is detected, use 'cloudflare' platform
  return (isCloudflareWorkers || isVercelEdge) ? 'cloudflare' : 'node';
};

/**
 * Pre-calculated target platform constant
 * 
 * This constant is computed once at module load time for optimal performance.
 * Use this for conditional logic that doesn't need to re-evaluate the platform.
 * 
 * @constant {TargetPlatform}
 * @example
 * ```typescript
 * // Preferred for performance-critical code
 * if (targetPlatform === 'cloudflare') {
 *   // Edge Runtime logic
 * }
 * 
 * // Conditional imports based on platform
 * const client = targetPlatform === 'cloudflare' 
 *   ? new EdgeClient() 
 *   : new NodeClient();
 * ```
 */
export const targetPlatform = getTargetPlatform();

/**
 * Checks if the current environment is an Edge Runtime (Cloudflare/Vercel Edge)
 * 
 * This is a convenience function that uses the pre-calculated platform constant
 * for optimal performance. Prefer this over calling getTargetPlatform() repeatedly.
 * 
 * @returns {boolean} True if running in Edge Runtime environment
 * 
 * @example
 * ```typescript
 * if (isCloudflareEnvironment()) {
 *   // Use fetch(), FormData, etc. (Edge-compatible APIs)
 *   const response = await fetch(url);
 * } else {
 *   // Can use Node.js APIs like fs, crypto, etc.
 *   const fs = require('fs');
 * }
 * ```
 */
export const isCloudflareEnvironment = (): boolean => {
  return targetPlatform === 'cloudflare';
};

/**
 * Checks if the current environment is Node.js runtime
 * 
 * This includes traditional Node.js servers, Vercel Serverless functions,
 * and any other Node.js-compatible environment.
 * 
 * @returns {boolean} True if running in Node.js environment
 * 
 * @example
 * ```typescript
 * if (isNodeEnvironment()) {
 *   // Can use full Node.js APIs
 *   const { readFileSync } = require('fs');
 *   const { createHash } = require('crypto');
 * }
 * ```
 */
export const isNodeEnvironment = (): boolean => {
  return targetPlatform === 'node';
};

/**
 * Helper for conditional module loading based on platform
 * 
 * This utility function simplifies platform-specific module selection,
 * which is useful for conditional imports and dependency injection.
 * 
 * @template T The type of the modules being selected
 * @param {T} cloudflareModule - Module to use in Cloudflare/Edge environment
 * @param {T} nodeModule - Module to use in Node.js environment
 * @returns {T} The appropriate module for the current platform
 * 
 * @example
 * ```typescript
 * // Conditional module selection
 * const storage = selectModuleByPlatform(
 *   new EdgeStorage(),
 *   new NodeStorage()
 * );
 * 
 * // With dynamic imports
 * const crypto = selectModuleByPlatform(
 *   await import('./crypto-edge'),
 *   await import('./crypto-node')
 * );
 * ```
 */
export const selectModuleByPlatform = <T>(
  cloudflareModule: T,
  nodeModule: T
): T => {
  return isCloudflareEnvironment() ? cloudflareModule : nodeModule;
};

/**
 * Gets detailed runtime information (only when needed for debugging/logging)
 * 
 * This function provides comprehensive information about the runtime environment,
 * including the detected platform and the specific characteristics that led to
 * the detection. Use sparingly as it re-runs detection logic.
 * 
 * @returns {object} Runtime detection details
 * @returns {TargetPlatform} returns.platform - The detected platform
 * @returns {boolean} returns.isExplicitlySet - Whether platform was set via DEPLOYMENT_TARGET
 * @returns {boolean} returns.isCloudflareWorkers - Whether Cloudflare Workers detected
 * @returns {boolean} returns.isVercelEdge - Whether Vercel Edge Runtime detected
 * 
 * @example
 * ```typescript
 * // For debugging or logging
 * const info = getRuntimeInfo();
 * console.log(`Platform: ${info.platform}`);
 * console.log(`Explicitly set: ${info.isExplicitlySet}`);
 * console.log(`Cloudflare Workers: ${info.isCloudflareWorkers}`);
 * console.log(`Vercel Edge: ${info.isVercelEdge}`);
 * ```
 */
export const getRuntimeInfo = () => {
  const { deploymentTarget, ...detection } = detectRuntime();
  return {
    platform: targetPlatform,
    isExplicitlySet: deploymentTarget !== undefined,
    ...detection,
  };
};
