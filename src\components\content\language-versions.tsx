/**
 * LanguageVersions Component
 *
 * =============================================================================
 * COMPONENT PURPOSE & USAGE SCENARIOS
 * =============================================================================
 *
 * This component is designed for CONTENT AREAS and SIDEBARS where you need
 * detailed language version information. It provides comprehensive language
 * switching with content titles and metadata display.
 *
 * CURRENT USAGE IN CODEBASE:
 * ⚠️  Currently NOT actively used in any pages (available for future use)
 * 🎯 Designed for: Sidebars, content management areas, detailed language info
 *
 * VISUAL CHARACTERISTICS:
 * - Uses Lucide React icons (Globe, Check, ExternalLink)
 * - Shows content type badges (Article, Product, Case Study)
 * - Displays actual content titles for each language
 * - More spacious design with detailed information
 * - Includes helpful tips and guidance text
 *
 * WHEN TO USE THIS COMPONENT:
 * ✅ Content sidebars with available space
 * ✅ Content management interfaces
 * ✅ When you need to show content titles
 * ✅ Admin panels or detailed language selection
 * ✅ Areas where comprehensive language info is needed
 *
 * WHEN NOT TO USE (use ContentLanguageIndicator instead):
 * ❌ Page headers (too large and detailed)
 * ❌ Compact spaces
 * ❌ Simple language switching needs
 *
 * COMPONENT COMPARISON:
 * ContentLanguageIndicator: Compact, status icons, headers
 * LanguageVersions: Detailed, content titles, sidebars
 *
 * =============================================================================
 */

"use client";

// UI Components - Shadcn/ui components for consistent design
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// Next.js Navigation Hooks - For routing and URL management
import { useParams, usePathname, useRouter } from "next/navigation";

// Toast Notifications - For user feedback during language switching
import { toast } from "sonner";

// Icons - Lucide React icons for visual elements
import { Globe, Check, ExternalLink } from "lucide-react";

// Internationalization - Locale configuration and names
import { localeNames, locales } from "@/i18n/locale";

// Content Management Services - Core functions for content language handling
import {
  detectContentPage,           // Detects content type and slug from URL
  getAvailableLanguageVersions, // Gets all language versions for content
  handleContentLanguageSwitch, // Handles intelligent language switching
  getContentTitle,             // Retrieves content title for display
  type ContentType             // TypeScript type for content categories
} from "@/services/content";

/**
 * Props interface for the LanguageVersions component
 */
interface LanguageVersionsProps {
  /**
   * Whether to show as a compact inline version
   *
   * USAGE EXAMPLES:
   *
   * compact={false} - Full card display (RECOMMENDED for sidebars):
   * ```tsx
   * // In content sidebars or dedicated language sections
   * <aside className="w-64">
   *   <LanguageVersions compact={false} />
   * </aside>
   * ```
   *
   * compact={true} - Inline horizontal display:
   * ```tsx
   * // In content areas where space is limited
   * <div className="content-footer">
   *   <LanguageVersions compact={true} />
   * </div>
   * ```
   *
   * COMPARISON WITH ContentLanguageIndicator:
   * - Use ContentLanguageIndicator for page headers (more compact, status icons)
   * - Use LanguageVersions for sidebars/content areas (more detailed, shows titles)
   */
  compact?: boolean;
  /**
   * Custom CSS class name for additional styling
   */
  className?: string;
}

/**
 * Language Versions Component
 *
 * PURPOSE:
 * This component is designed for CONTENT AREAS and SIDEBARS where you want to show
 * a comprehensive view of language versions. It provides a more detailed and
 * informative display compared to ContentLanguageIndicator.
 *
 * USAGE LOCATIONS:
 * - Content sidebars (detailed language information)
 * - Content management interfaces
 * - Admin panels or content editing areas
 * - Anywhere you need detailed language version information
 *
 * KEY DIFFERENCES FROM ContentLanguageIndicator:
 * - More detailed information display (shows content titles)
 * - Larger, more prominent design
 * - Uses Lucide React icons (Globe, Check, ExternalLink)
 * - Better for areas with more space
 * - Shows content type badges (Article, Product, Case Study)
 * - Includes helpful tips and guidance text
 *
 * VISUAL DESIGN:
 * - Compact: Horizontal layout with language buttons and availability
 * - Full: Card layout with content type, titles, and detailed language info
 *
 * BEHAVIOR:
 * - Only appears on content detail pages (not list pages)
 * - Shows fewer languages if only one version exists
 * - Displays actual content titles for each language version
 * - Provides comprehensive language switching interface
 *
 * WHEN TO USE:
 * - Use ContentLanguageIndicator for headers/compact spaces
 * - Use LanguageVersions for sidebars/detailed information areas
 *
 * @param compact - Display mode (false for full card, true for inline)
 * @param className - Additional CSS classes for styling
 */
export default function LanguageVersions({
  compact = false,
  className = ""
}: LanguageVersionsProps) {
  // Next.js hooks for navigation and URL management
  const params = useParams();                    // Get URL parameters (including locale)
  const locale = params.locale as string;        // Extract current locale from URL
  const router = useRouter();                    // Router for programmatic navigation
  const pathname = usePathname();                // Current pathname for content detection

  // Detect current content page information from URL
  // This analyzes the pathname to determine content type and slug
  const contentInfo = detectContentPage(pathname, locale);

  // Early return: Only show component for actual content pages with slugs
  // Prevents showing on list pages or non-content pages
  if (contentInfo.type === 'other' || !contentInfo.slug) {
    return null;
  }

  // Get all available language versions for this specific content
  // This checks which languages have translations of the current content
  const languageVersions = getAvailableLanguageVersions(
    contentInfo.type,    // Content type (blog, product, case-study)
    contentInfo.slug,    // Content identifier (slug)
    locales             // All supported locales from i18n config
  );

  // Filter to only versions that actually exist
  // This helps determine if we should show the component at all
  const availableVersions = languageVersions.filter(version => version.exists);

  // Early return: Don't show if only one language version exists
  // No point in showing language switcher for single-language content
  if (availableVersions.length <= 1) {
    return null;
  }

  /**
   * Handle language switching with intelligent fallback
   *
   * This function attempts to switch to the target language version of the current content.
   * If the content doesn't exist in the target language, it falls back to the content list
   * page and shows a user-friendly notification.
   *
   * @param targetLocale - The locale to switch to (e.g., 'en', 'zh')
   */
  const handleLanguageSwitch = (targetLocale: string) => {
    // Prevent unnecessary switching to the same language
    if (targetLocale === locale) return;

    // Use the intelligent language switching utility
    // This handles both direct switching and fallback scenarios
    const switchResult = handleContentLanguageSwitch(
      pathname,      // Current page path
      locale,        // Current language
      targetLocale   // Target language
    );

    // Navigate to the determined URL (either direct content or fallback list)
    router.push(switchResult.url);

    // Provide user feedback for fallback scenarios
    // When content doesn't exist in target language, user is redirected to list page
    if (switchResult.strategy === 'fallback-list') {
      toast.info(
        `This content is not available in ${localeNames[targetLocale]}. Redirected to the content list.`,
        { duration: 4000 }
      );
    }
  };

  /**
   * Get user-friendly label for content type
   *
   * Converts internal content type identifiers to display-friendly labels
   * for better user experience in the UI.
   *
   * @param type - Internal content type identifier
   * @returns Human-readable content type label
   */
  const getContentTypeLabel = (type: ContentType): string => {
    switch (type) {
      case 'blog':
        return 'Article';
      case 'product':
        return 'Product';
      case 'case-study':
        return 'Case Study';
      default:
        return 'Content';
    }
  };

  // Render compact version - horizontal inline display
  if (compact) {
    return (
      <div className={`flex items-center gap-2 text-sm text-muted-foreground ${className}`}>
        {/* Globe icon to indicate language options */}
        <Globe className="h-4 w-4" />
        <span>Available in:</span>

        {/* Language buttons/badges container */}
        <div className="flex gap-1">
          {languageVersions.map((version) => (
            <TooltipProvider key={version.locale}>
              <Tooltip>
                <TooltipTrigger asChild>
                  {/* Render clickable button for available languages */}
                  {version.exists ? (
                    <Button
                      variant={version.locale === locale ? "default" : "outline"}
                      size="sm"
                      className="h-6 px-2 text-xs"
                      onClick={() => handleLanguageSwitch(version.locale)}
                      disabled={version.locale === locale}
                    >
                      {/* Show check icon for current language */}
                      {version.locale === locale && <Check className="h-3 w-3 mr-1" />}
                      {localeNames[version.locale]}
                    </Button>
                  ) : (
                    /* Render disabled badge for unavailable languages */
                    <Badge variant="secondary" className="h-6 px-2 text-xs opacity-50">
                      {localeNames[version.locale]}
                    </Badge>
                  )}
                </TooltipTrigger>

                {/* Tooltip with contextual information */}
                <TooltipContent>
                  {version.exists
                    ? version.locale === locale
                      ? "Current language"
                      : `Switch to ${localeNames[version.locale]}`
                    : `Not available in ${localeNames[version.locale]}`
                  }
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          ))}
        </div>
      </div>
    );
  }

  // Render full version - detailed card display
  return (
    <Card className={`mb-6 ${className}`}>
      <CardContent className="p-4">
        {/* Card header with title and content type badge */}
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-2 mb-3">
            <Globe className="h-5 w-5 text-muted-foreground" />
            <h3 className="font-medium">Language Versions</h3>
          </div>
          {/* Content type indicator badge */}
          <Badge variant="outline" className="text-xs">
            {getContentTypeLabel(contentInfo.type)}
          </Badge>
        </div>

        {/* Descriptive text explaining the purpose */}
        <p className="text-sm text-muted-foreground mb-4">
          This {getContentTypeLabel(contentInfo.type).toLowerCase()} is available in the following languages:
        </p>

        {/* Language versions list */}
        <div className="space-y-2">
          {languageVersions.map((version, index) => {
            const isCurrentLanguage = version.locale === locale;
            // Get the actual title of the content in this language (if available)
            const currentTitle = getContentTitle(contentInfo.type, contentInfo.slug!, version.locale);

            return (
              <div key={version.locale}>
                {/* Individual language version row */}
                <div className="flex items-center justify-between p-3 rounded-lg border bg-muted/30">
                  {/* Left side: Language info and title */}
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                      {/* Check icon for current language */}
                      {isCurrentLanguage && <Check className="h-4 w-4 text-green-600" />}
                      <span className="font-medium">
                        {localeNames[version.locale]}
                      </span>
                      {/* Current language badge */}
                      {isCurrentLanguage && (
                        <Badge variant="default" className="text-xs">
                          Current
                        </Badge>
                      )}
                    </div>
                    {/* Show content title if available */}
                    {currentTitle && (
                      <span className="text-sm text-muted-foreground truncate max-w-xs">
                        &quot;{currentTitle}&quot;
                      </span>
                    )}
                  </div>

                  {/* Right side: Action buttons */}
                  <div className="flex items-center gap-2">
                    {version.exists ? (
                      /* Show switch button for available languages (except current) */
                      !isCurrentLanguage && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleLanguageSwitch(version.locale)}
                          className="flex items-center gap-1"
                        >
                          <ExternalLink className="h-3 w-3" />
                          Switch
                        </Button>
                      )
                    ) : (
                      /* Show unavailable badge for missing translations */
                      <Badge variant="secondary" className="text-xs">
                        Not Available
                      </Badge>
                    )}
                  </div>
                </div>

                {/* Separator between language versions (except last) */}
                {index < languageVersions.length - 1 && (
                  <Separator className="my-2" />
                )}
              </div>
            );
          })}
        </div>

        {/* Footer with helpful tip */}
        <div className="mt-4 pt-3 border-t">
          <p className="text-xs text-muted-foreground">
            💡 You can also use the language selector in the header to switch between languages.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
