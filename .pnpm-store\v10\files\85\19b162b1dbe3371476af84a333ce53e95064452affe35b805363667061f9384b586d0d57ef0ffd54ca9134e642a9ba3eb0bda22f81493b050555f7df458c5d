// ets_tracing: off

import "../../../../Operator/index.js"

export const _ChannelTypeId = Symbol()
export type _ChannelTypeId = typeof _ChannelTypeId
export const _Env = Symbol()
export type _Env = typeof _Env
export const _InErr = Symbol()
export type _InErr = typeof _InErr
export const _InElem = Symbol()
export type _InElem = typeof _InElem
export const _InDone = Symbol()
export type _InDone = typeof _InDone
export const _OutErr = Symbol()
export type _OutErr = typeof _OutErr
export const _OutErr2 = Symbol()
export type _OutErr2 = typeof _OutErr2
export const _OutElem = Symbol()
export type _OutElem = typeof _OutElem
export const _OutDone = Symbol()
export type _OutDone = typeof _OutDone
export const _OutDone2 = Symbol()
export type _OutDone2 = typeof _OutDone2
