import { loader } from "fumadocs-core/source";
import { docs } from "@/.source";
import { createFromSource } from "fumadocs-core/search/server";
import { icons } from "lucide-react";
import { createElement } from "react";

// Create a modified source with only English language to avoid "zh" language issues
const searchSource = loader({
  baseUrl: "/docs",
  source: docs.toFumadocsSource(),
  // Only use English for search to avoid language support issues
  i18n: {
    defaultLanguage: "en",
    languages: ["en"], // Only English to avoid "zh" language error
  },
  icon(icon) {
    if (!icon) {
      return;
    }
    if (icon in icons) return createElement(icons[icon as keyof typeof icons]);
  },
});

export const { GET } = createFromSource(searchSource, {
  // https://docs.orama.com/open-source/supported-languages
  language: "english",
});
