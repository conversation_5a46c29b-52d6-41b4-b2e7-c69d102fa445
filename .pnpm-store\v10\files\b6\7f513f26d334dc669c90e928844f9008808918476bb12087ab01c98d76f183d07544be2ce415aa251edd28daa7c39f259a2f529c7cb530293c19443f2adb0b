import type * as CL from "../../../../Clock/index.js";
import type * as CK from "../../../../Collections/Immutable/Chunk/index.js";
import type * as C from "../core.js";
/**
 * Delays the chunks of this stream according to the given bandwidth parameters using the token bucket
 * algorithm. Allows for burst in the processing of elements by allowing the token bucket to accumulate
 * tokens up to a `units + burst` threshold. The weight of each chunk is determined by the `costFn`
 * function.
 */
export declare function throttleShape_<R, E, A>(self: C.Stream<R, E, A>, units: number, duration: number, costFn: (a: CK.Chunk<A>) => number, burst?: number): C.Stream<CL.HasClock & R, E, A>;
/**
 * Delays the chunks of this stream according to the given bandwidth parameters using the token bucket
 * algorithm. Allows for burst in the processing of elements by allowing the token bucket to accumulate
 * tokens up to a `units + burst` threshold. The weight of each chunk is determined by the `costFn`
 * function.
 *
 * @ets_data_first throttleShape_
 */
export declare function throttleShape<A>(units: number, duration: number, costFn: (a: CK.Chunk<A>) => number, burst?: number): <R, E>(self: C.Stream<R, E, A>) => C.Stream<CL.HasClock & R, E, A>;
//# sourceMappingURL=throttleShape.d.ts.map