/**
 * Cache Service Module
 * 
 * Unified cache service that provides a high-level interface for caching
 * operations across different cache providers. This service automatically
 * handles provider selection, key generation, and cache invalidation.
 * 
 * Key Features:
 * - Multiple cache provider support (memory, Redis, file)
 * - Automatic key generation and namespacing
 * - Content-aware caching strategies
 * - Cache invalidation patterns
 * - Performance monitoring and statistics
 */

import type { 
  CacheService, 
  CacheProvider, 
  CacheConfig, 
  CacheStats,
  CacheKeyBuilder,
  CacheMiddleware,
  ContentItem,
  ContentType
} from '../../types'

import { MemoryCacheProvider } from './memory-cache'

/**
 * Cache Key Builder Implementation
 * 
 * Generates consistent cache keys for different types of content
 * and operations, ensuring uniqueness and enabling efficient
 * cache invalidation patterns.
 */
export class CacheKeyBuilderImpl implements CacheKeyBuilder {
  private prefix: string

  constructor(prefix: string = 'cms') {
    this.prefix = prefix
  }

  contentKey(type: ContentType, slug: string, locale: string): string {
    return `${this.prefix}:content:${type}:${locale}:${slug}`
  }

  contentListKey(type: ContentType, locale: string, options?: Record<string, unknown>): string {
    const optionsHash = options ? this.hashOptions(options) : 'default'
    return `${this.prefix}:list:${type}:${locale}:${optionsHash}`
  }

  metadataKey(type: ContentType, slug: string, locale: string): string {
    return `${this.prefix}:metadata:${type}:${locale}:${slug}`
  }

  seoKey(type: ContentType, slug: string, locale: string): string {
    return `${this.prefix}:seo:${type}:${locale}:${slug}`
  }

  sitemapKey(locale?: string): string {
    return `${this.prefix}:sitemap${locale ? `:${locale}` : ''}`
  }

  rssKey(locale: string): string {
    return `${this.prefix}:rss:${locale}`
  }

  parseKey(key: string): { type: string; identifier: string; locale?: string } {
    const parts = key.split(':')
    return {
      type: parts[1] || 'unknown',
      identifier: parts.slice(2).join(':'),
      locale: parts[3]
    }
  }

  generateTags(key: string): string[] {
    const parsed = this.parseKey(key)
    const tags = [parsed.type]
    
    if (parsed.locale) {
      tags.push(`locale:${parsed.locale}`)
    }
    
    return tags
  }

  private hashOptions(options: Record<string, unknown>): string {
    try {
      const sorted = JSON.stringify(options, Object.keys(options).sort())
      return Buffer.from(sorted).toString('base64').slice(0, 8)
    } catch {
      return 'invalid'
    }
  }
}

/**
 * Cache Service Implementation
 * 
 * Main cache service that coordinates between different cache providers
 * and provides content-specific caching functionality with automatic
 * key generation and invalidation.
 */
export class CacheServiceImpl implements CacheService {
  private provider: CacheProvider
  private keyBuilder: CacheKeyBuilder
  private middleware: CacheMiddleware[] = []
  private config: CacheConfig

  constructor(config: CacheConfig) {
    this.config = config
    this.keyBuilder = new CacheKeyBuilderImpl()
    
    // Initialize cache provider based on strategy
    switch (config.strategy) {
      case 'memory':
        this.provider = new MemoryCacheProvider(config.memory)
        break
      
      case 'redis':
        // TODO: Implement Redis provider
        throw new Error('Redis cache provider not yet implemented')
      
      case 'file':
        // TODO: Implement file cache provider
        throw new Error('File cache provider not yet implemented')
      
      case 'hybrid':
        // TODO: Implement hybrid cache provider
        throw new Error('Hybrid cache provider not yet implemented')
      
      default:
        throw new Error(`Unknown cache strategy: ${config.strategy}`)
    }
  }

  /**
   * Cache content item
   * 
   * Stores a content item in the cache with appropriate TTL
   * and tags for efficient retrieval and invalidation.
   * 
   * @param type - Content type
   * @param slug - Content slug
   * @param locale - Language locale
   * @param content - Content item to cache
   */
  async cacheContent<T extends ContentItem>(
    type: ContentType,
    slug: string,
    locale: string,
    content: T
  ): Promise<void> {
    const key = this.keyBuilder.contentKey(type, slug, locale)
    const ttl = this.config.ttl.content
    
    await this.executeWithMiddleware('set', key, async () => {
      await this.provider.set(key, content, ttl)
      
      // Add tags for invalidation
      const tags = [
        `content:${type}`,
        `locale:${locale}`,
        `content:${type}:${locale}`
      ]
      await this.provider.tagKey(key, tags)
    })
  }

  /**
   * Get cached content item
   * 
   * Retrieves a content item from the cache if available
   * and not expired.
   * 
   * @param type - Content type
   * @param slug - Content slug
   * @param locale - Language locale
   * @returns Cached content item or null
   */
  async getCachedContent<T extends ContentItem>(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<T | null> {
    const key = this.keyBuilder.contentKey(type, slug, locale)
    
    return this.executeWithMiddleware('get', key, async () => {
      return this.provider.get<T>(key)
    })
  }

  /**
   * Cache content list
   * 
   * Stores a list of content items with query options
   * for efficient list retrieval.
   * 
   * @param type - Content type
   * @param locale - Language locale
   * @param content - Array of content items
   * @param options - Query options used to generate the list
   */
  async cacheContentList<T extends ContentItem>(
    type: ContentType,
    locale: string,
    content: T[],
    options?: Record<string, unknown>
  ): Promise<void> {
    const key = this.keyBuilder.contentListKey(type, locale, options)
    const ttl = this.config.ttl.contentList
    
    await this.executeWithMiddleware('set', key, async () => {
      await this.provider.set(key, content, ttl)
      
      // Add tags for invalidation
      const tags = [
        `list:${type}`,
        `locale:${locale}`,
        `list:${type}:${locale}`
      ]
      await this.provider.tagKey(key, tags)
    })
  }

  /**
   * Get cached content list
   * 
   * Retrieves a cached list of content items if available.
   * 
   * @param type - Content type
   * @param locale - Language locale
   * @param options - Query options to match cached list
   * @returns Cached content list or null
   */
  async getCachedContentList<T extends ContentItem>(
    type: ContentType,
    locale: string,
    options?: Record<string, unknown>
  ): Promise<T[] | null> {
    const key = this.keyBuilder.contentListKey(type, locale, options)
    
    return this.executeWithMiddleware('get', key, async () => {
      return this.provider.get<T[]>(key)
    })
  }

  /**
   * Cache SEO data
   * 
   * Stores SEO-related data with appropriate TTL.
   * 
   * @param key - Cache key for SEO data
   * @param data - SEO data to cache
   */
  async cacheSEOData(key: string, data: unknown): Promise<void> {
    const ttl = this.config.ttl.seo
    
    await this.executeWithMiddleware('set', key, async () => {
      await this.provider.set(key, data, ttl)
      
      // Add SEO tag
      await this.provider.tagKey(key, ['seo'])
    })
  }

  /**
   * Get cached SEO data
   * 
   * Retrieves cached SEO data if available.
   * 
   * @param key - Cache key for SEO data
   * @returns Cached SEO data or null
   */
  async getCachedSEOData<T>(key: string): Promise<T | null> {
    return this.executeWithMiddleware('get', key, async () => {
      return this.provider.get<T>(key)
    })
  }

  /**
   * Invalidate content cache
   * 
   * Removes cached content based on type, slug, and locale.
   * Supports partial invalidation for efficient cache management.
   * 
   * @param type - Content type to invalidate
   * @param slug - Optional specific slug to invalidate
   * @param locale - Optional specific locale to invalidate
   */
  async invalidateContent(
    type: ContentType, 
    slug?: string, 
    locale?: string
  ): Promise<void> {
    if (slug && locale) {
      // Invalidate specific content item
      const contentKey = this.keyBuilder.contentKey(type, slug, locale)
      const metadataKey = this.keyBuilder.metadataKey(type, slug, locale)
      const seoKey = this.keyBuilder.seoKey(type, slug, locale)
      
      await this.provider.deleteMany([contentKey, metadataKey, seoKey])
    } else if (locale) {
      // Invalidate all content of type for specific locale
      await this.provider.deleteByTag(`content:${type}:${locale}`)
      await this.provider.deleteByTag(`list:${type}:${locale}`)
    } else {
      // Invalidate all content of type
      await this.provider.deleteByTag(`content:${type}`)
      await this.provider.deleteByTag(`list:${type}`)
    }
  }

  /**
   * Invalidate all cache
   * 
   * Clears all cached data. Use with caution in production.
   */
  async invalidateAll(): Promise<void> {
    await this.provider.clear()
  }

  /**
   * Get cache statistics
   * 
   * Returns comprehensive statistics about cache performance.
   * 
   * @returns Cache statistics
   */
  async getStats(): Promise<CacheStats> {
    return this.provider.stats()
  }

  /**
   * Add middleware
   * 
   * Registers middleware for cache operations monitoring
   * and custom processing.
   * 
   * @param middleware - Middleware to add
   */
  use(middleware: CacheMiddleware): void {
    this.middleware.push(middleware)
  }

  /**
   * Connect to cache provider
   * 
   * Initializes connection to the cache backend.
   */
  async connect(): Promise<void> {
    if (this.provider.connect) {
      await this.provider.connect()
    }
  }

  /**
   * Disconnect from cache provider
   * 
   * Closes connection to the cache backend.
   */
  async disconnect(): Promise<void> {
    if (this.provider.disconnect) {
      await this.provider.disconnect()
    }
  }

  // Private helper methods

  private async executeWithMiddleware<T>(
    operation: string,
    key: string,
    fn: () => Promise<T>
  ): Promise<T> {
    // Execute before hooks
    for (const middleware of this.middleware) {
      if (operation === 'get' && middleware.beforeGet) {
        await middleware.beforeGet(key)
      } else if (operation === 'set' && middleware.beforeSet) {
        await middleware.beforeSet(key, undefined)
      }
    }

    try {
      const result = await fn()

      // Execute after hooks
      for (const middleware of this.middleware) {
        if (operation === 'get' && middleware.afterGet) {
          await middleware.afterGet(key, result, result !== null)
        } else if (operation === 'set' && middleware.afterSet) {
          await middleware.afterSet(key, result)
        }
      }

      return result
    } catch (error) {
      // Execute error hooks
      for (const middleware of this.middleware) {
        if (middleware.onError) {
          await middleware.onError(error as Error, operation, key)
        }
      }
      throw error
    }
  }
}

/**
 * Create cache service instance
 * 
 * Factory function to create a configured cache service instance.
 * 
 * @param config - Cache configuration
 * @returns Configured cache service
 */
export function createCacheService(config: CacheConfig): CacheService {
  return new CacheServiceImpl(config)
}

// Export default configuration
export const defaultCacheConfig: CacheConfig = {
  strategy: 'memory',
  ttl: {
    content: 3600, // 1 hour
    contentList: 1800, // 30 minutes
    metadata: 7200, // 2 hours
    seo: 3600, // 1 hour
    sitemap: 86400, // 24 hours
    rss: 3600 // 1 hour
  },
  maxSize: 100 * 1024 * 1024, // 100MB
  maxEntries: 10000,
  memory: {
    maxSize: 100 * 1024 * 1024,
    gcInterval: 60000 // 1 minute
  },
  invalidation: {
    enabled: true,
    patterns: ['content:*', 'list:*', 'seo:*'],
    onContentChange: true,
    onBuild: true
  }
}

// Export singleton instance
export const cacheService = createCacheService(defaultCacheConfig)
