#!/usr/bin/env tsx

/**
 * CMS ESLint Check Script
 * 
 * This script checks ESLint warnings specifically for the CMS module
 * to verify that our code quality improvements have been successful.
 */

import { execSync } from 'child_process'

interface ESLintResult {
  file: string
  warnings: number
  errors: number
  issues: string[]
}

class CMSESLintChecker {
  private cmsFiles = [
    'src/cms/types/index.ts',
    'src/cms/types/cache.ts',
    'src/cms/types/seo.ts',
    'src/cms/services/index.ts',
    'src/cms/services/seo.ts',
    'src/cms/services/cache/index.ts',
    'src/cms/providers/contentlayer2/provider.ts',
    'src/cms/__tests__/cms-service.test.ts',
    'src/cms/__tests__/contentlayer2-provider.test.ts'
  ]

  async checkCMSModule(): Promise<void> {
    console.log('🔍 Checking ESLint status for CMS module...\n')

    const results: ESLintResult[] = []
    let totalWarnings = 0
    let totalErrors = 0

    for (const file of this.cmsFiles) {
      try {
        console.log(`Checking: ${file}`)
        
        // Run ESLint on the specific file
        const output = execSync(`npx eslint ${file} --format json`, {
          encoding: 'utf-8',
          stdio: 'pipe'
        })

        const eslintResults = JSON.parse(output)
        
        if (eslintResults.length > 0) {
          const fileResult = eslintResults[0]
          const warnings = fileResult.warningCount || 0
          const errors = fileResult.errorCount || 0
          const issues: string[] = fileResult.messages?.map((msg: any) =>
            `${msg.line}:${msg.column} ${msg.severity === 1 ? 'Warning' : 'Error'}: ${msg.message} (${msg.ruleId})`
          ) || []

          results.push({
            file,
            warnings,
            errors,
            issues
          })

          totalWarnings += warnings
          totalErrors += errors

          if (warnings > 0 || errors > 0) {
            console.log(`  ⚠️  ${warnings} warnings, ${errors} errors`)
            issues.forEach(issue => console.log(`    ${issue}`))
          } else {
            console.log(`  ✅ No issues`)
          }
        } else {
          console.log(`  ✅ No issues`)
        }
      } catch (error: any) {
        if (error.status === 1) {
          // ESLint found issues, parse the output
          try {
            const eslintResults = JSON.parse(error.stdout)
            if (eslintResults.length > 0) {
              const fileResult = eslintResults[0]
              const warnings = fileResult.warningCount || 0
              const errors = fileResult.errorCount || 0
              const issues = fileResult.messages?.map((msg: any) => 
                `${msg.line}:${msg.column} ${msg.severity === 1 ? 'Warning' : 'Error'}: ${msg.message} (${msg.ruleId})`
              ) || []

              results.push({
                file,
                warnings,
                errors,
                issues
              })

              totalWarnings += warnings
              totalErrors += errors

              console.log(`  ⚠️  ${warnings} warnings, ${errors} errors`)
              issues.forEach((issue: string) => console.log(`    ${issue}`))
            }
          } catch (parseError: any) {
            console.log(`  ❌ Error checking file: ${parseError.message}`)
          }
        } else {
          console.log(`  ❌ Error checking file: ${error.message}`)
        }
      }
      
      console.log('')
    }

    this.printSummary(results, totalWarnings, totalErrors)
  }

  private printSummary(results: ESLintResult[], totalWarnings: number, totalErrors: number): void {
    console.log('📊 CMS Module ESLint Summary:')
    console.log(`  Total files checked: ${this.cmsFiles.length}`)
    console.log(`  Total warnings: ${totalWarnings}`)
    console.log(`  Total errors: ${totalErrors}`)
    
    const filesWithIssues = results.filter(r => r.warnings > 0 || r.errors > 0)
    console.log(`  Files with issues: ${filesWithIssues.length}`)
    
    if (filesWithIssues.length === 0) {
      console.log('\n🎉 All CMS module files are ESLint clean!')
      console.log('\n✅ Code quality improvements completed successfully:')
      console.log('  - Removed unused imports and variables')
      console.log('  - Replaced `any` types with proper TypeScript types')
      console.log('  - Fixed anonymous default exports')
      console.log('  - Improved type safety across the CMS module')
    } else {
      console.log('\n⚠️  Some issues remain:')
      filesWithIssues.forEach(result => {
        console.log(`\n  ${result.file}:`)
        console.log(`    ${result.warnings} warnings, ${result.errors} errors`)
      })
    }

    console.log('\n📋 Next Steps:')
    if (totalErrors > 0) {
      console.log('  1. Fix remaining ESLint errors (blocking issues)')
    }
    if (totalWarnings > 0) {
      console.log('  2. Address remaining ESLint warnings (code quality)')
    }
    console.log('  3. Run full build to verify no regressions')
    console.log('  4. Test CMS functionality to ensure everything works')
  }
}

// Run the checker
async function main() {
  const checker = new CMSESLintChecker()
  await checker.checkCMSModule()
}

main().catch(error => {
  console.error('❌ Script failed:', error)
  process.exit(1)
})
