/**
 * Safe environment variable access utility
 * Prevents "Cannot read properties of undefined (reading 'env')" errors
 * during build/prerendering phases
 */

/**
 * Safely get an environment variable value
 * @param key - Environment variable key
 * @param defaultValue - Default value if env var is not available
 * @returns Environment variable value or default
 */
export function getEnv(key: string, defaultValue: string = ''): string {
  try {
    if (typeof process !== 'undefined' && process.env && typeof process.env[key] === 'string') {
      return process.env[key] as string;
    }
    return defaultValue;
  } catch (error) {
    console.warn(`Failed to access environment variable ${key}:`, error);
    return defaultValue;
  }
}

/**
 * Safely check if an environment variable is set to 'true'
 * @param key - Environment variable key
 * @returns true if env var is 'true', false otherwise
 */
export function getEnvBoolean(key: string, defaultValue: boolean = false): boolean {
  try {
    const value = getEnv(key);
    return value === 'true';
  } catch (error) {
    console.warn(`Failed to access boolean environment variable ${key}:`, error);
    return defaultValue;
  }
}

/**
 * Common environment variables with safe access
 */
export const env = {
  get NEXT_PUBLIC_WEB_URL() {
    return getEnv('NEXT_PUBLIC_WEB_URL');
  },
  get NEXT_PUBLIC_PROJECT_NAME() {
    return getEnv('NEXT_PUBLIC_PROJECT_NAME', 'ShipAny');
  },
  get NEXT_PUBLIC_APP_ID() {
    return getEnv('NEXT_PUBLIC_APP_ID', 'shipany-default');
  },
  get NEXT_PUBLIC_DEFAULT_THEME() {
    return getEnv('NEXT_PUBLIC_DEFAULT_THEME', 'system');
  },
  get NEXT_PUBLIC_GOOGLE_ADCODE() {
    return getEnv('NEXT_PUBLIC_GOOGLE_ADCODE');
  },
  get NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED() {
    return getEnvBoolean('NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED');
  },
  get NEXT_PUBLIC_AUTH_GOOGLE_ID() {
    return getEnv('NEXT_PUBLIC_AUTH_GOOGLE_ID');
  },
  get DEPLOYMENT_TARGET() {
    return getEnv('DEPLOYMENT_TARGET');
  },
  get NEXT_RUNTIME() {
    return getEnv('NEXT_RUNTIME');
  },
  get LOGGER_LEVEL() {
    return getEnv('LOGGER_LEVEL');
  },
};
