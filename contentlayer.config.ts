/**
 * Contentlayer Configuration
 *
 * This file configures Contentlayer to process MDX files for our multi-language
 * content management system. Contentlayer transforms MDX files into type-safe
 * TypeScript objects that can be imported and used throughout the application.
 *
 * Key Features:
 * - Multi-language support (en/zh) with automatic language detection
 * - Unified schema for blogs, products, and case studies
 * - Automatic URL generation and metadata extraction
 * - Type-safe content access with full TypeScript support
 * - File system-based content organization
 *
 * Directory Structure:
 * content/
 * ├── blogs/
 * │   ├── en/*.mdx
 * │   └── zh/*.mdx
 * ├── products/
 * │   ├── en/*.mdx
 * │   └── zh/*.mdx
 * └── case-studies/
 *     ├── en/*.mdx
 *     └── zh/*.mdx
 */

// Contentlayer Core Functions
import { defineDocumentType, makeSource } from 'contentlayer2/source-files'
// Node.js File System Modules - For file metadata extraction
import fs from 'fs'
import path from 'path'

/**
 * Factory function to create common document type configuration
 *
 * This function creates a reusable configuration object for different content types
 * (blogs, products, case studies) with consistent field definitions and computed
 * properties. It ensures all content types have the same structure and capabilities.
 *
 * @param name - The document type name for TypeScript generation (e.g., 'Blog', 'Product')
 * @param pattern - The directory pattern to match files (e.g., 'blogs', 'products')
 * @returns Document type configuration object for Contentlayer
 */
const makeCommon = (name: string, pattern: string) => ({
  name,
  // File Pattern Matching - Matches all MDX files in the specified directory structure
  // Example: 'blogs/**/*.mdx' matches 'blogs/en/post.mdx', 'blogs/zh/article.mdx'
  filePathPattern: `${pattern}/**/*.mdx`,
  contentType: 'mdx' as const,

  // Frontmatter Field Definitions - These fields can be defined in MDX file headers
  fields: {
    // Required Fields
    title: { type: 'string' as const, required: true },           // Content title for display
    slug: { type: 'string' as const, required: true },            // URL slug for routing

    // SEO and Metadata Fields
    description: { type: 'string' as const, required: false },    // Meta description for SEO
    coverImage: { type: 'string' as const, required: false },     // Hero/cover image URL

    // Author Information
    author: { type: 'string' as const, required: false },         // Author name
    authorImage: { type: 'string' as const, required: false },    // Author avatar URL

    // Publishing and Organization
    publishedAt: { type: 'date' as const, required: false },      // Publication date
    featured: { type: 'boolean' as const, required: false, default: false }, // Featured content flag
    tags: { type: 'list' as const, of: { type: 'string' as const }, required: false }, // Content tags/categories

    // Video Content Support
    videoUrl: { type: 'string' as const, required: false },       // Self-hosted video URL
    videoThumbnail: { type: 'string' as const, required: false }, // Video thumbnail/poster image
    videoDuration: { type: 'string' as const, required: false },  // Video duration (e.g., "5:30")
  },

  // Computed Fields - Automatically generated properties based on file path and metadata
  // These fields are calculated at build time and don't need to be defined in frontmatter
  computedFields: {
    /**
     * Language Detection from File Path
     *
     * Automatically extracts the language code from the file path structure.
     * Example: 'blogs/en/post.mdx' -> 'en', 'products/zh/item.mdx' -> 'zh'
     * This enables automatic language-based content organization and filtering.
     */
    lang: {
      type: 'string' as const,
      resolve: (doc: any) => doc._raw.flattenedPath.split('/')[1]
    },

    /**
     * URL Generation
     *
     * Automatically generates the full URL path for the content based on its
     * file structure and slug. This ensures consistent URL patterns across
     * all content types and languages.
     *
     * Format: /{contentType}/{language}/{slug}
     * Example: '/blogs/en/my-post', '/products/zh/my-product'
     */
    url: {
      type: 'string' as const,
      resolve: (doc: any) => `/${doc._raw.flattenedPath.split('/')[0]}/${doc._raw.flattenedPath.split('/')[1]}/${doc.slug}`
    },

    /**
     * File Creation Time
     *
     * Extracts the file creation timestamp from the filesystem. This provides
     * a fallback date for content that doesn't have an explicit publishedAt date
     * and can be used for sorting and organization.
     */
    createdAt: {
      type: 'date' as const,
      resolve: (doc: any) => fs.statSync(path.join('content', doc._raw.sourceFilePath)).birthtime
    }
  }
})

/**
 * Document Type Definitions
 *
 * These create specific document types for each content category using the
 * common configuration. Each type will have its own TypeScript interface
 * generated by Contentlayer for type-safe content access.
 */
export const Blog = defineDocumentType(() => makeCommon('Blog', 'blogs'))
export const Product = defineDocumentType(() => makeCommon('Product', 'products'))
export const CaseStudy = defineDocumentType(() => makeCommon('CaseStudy', 'case-studies'))

/**
 * Main Contentlayer Configuration
 *
 * This is the primary configuration object that tells Contentlayer how to process
 * content files. It specifies the content directory and which document types to
 * generate. When Contentlayer runs, it will:
 *
 * 1. Scan the 'content' directory for MDX files
 * 2. Match files to document types based on their path patterns
 * 3. Process frontmatter and content according to field definitions
 * 4. Generate TypeScript types and data objects
 * 5. Create the .contentlayer/generated directory with all processed content
 *
 * The generated content can then be imported and used throughout the application
 * with full TypeScript support and type safety.
 */
export default makeSource({
  contentDirPath: 'content',                           // Root directory for content files
  documentTypes: [Blog, Product, CaseStudy],          // Document types to process

  // Disable import alias warning for Windows compatibility
  disableImportAliasWarning: true,
})
