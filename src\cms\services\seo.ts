/**
 * SEO Service Implementation
 * 
 * Comprehensive SEO service that generates structured data, meta tags,
 * sitemaps, and RSS feeds for improved search engine optimization.
 * This service works with any CMS provider through the abstraction layer.
 * 
 * Key Features:
 * - JSON-LD structured data generation
 * - Open Graph and Twitter Card meta tags
 * - Multi-language sitemap generation with hreflang
 * - RSS feed generation for content syndication
 * - SEO analytics and recommendations
 */

import type { 
  ContentItem, 
  ContentType,
  SEOService,
  MetaTags,
  StructuredData,
  SitemapConfig,
  RSSConfig,
  BlogPostingStructuredData,
  ProductStructuredData,
  ArticleStructuredData
} from '../types'

/**
 * SEO Service Implementation
 * 
 * Main service class that handles all SEO-related operations including
 * structured data generation, meta tag creation, and sitemap/RSS generation.
 */
export class SEOServiceImpl implements SEOService {
  private baseUrl: string
  private siteName: string
  private defaultAuthor: string
  private defaultImage: string

  constructor(config: {
    baseUrl: string
    siteName: string
    defaultAuthor: string
    defaultImage: string
  }) {
    this.baseUrl = config.baseUrl.replace(/\/$/, '') // Remove trailing slash
    this.siteName = config.siteName
    this.defaultAuthor = config.defaultAuthor
    this.defaultImage = config.defaultImage
  }

  /**
   * Generate comprehensive meta tags for content
   * 
   * Creates all necessary meta tags including basic meta tags,
   * Open Graph tags, Twitter Cards, and structured data for
   * optimal SEO and social media sharing.
   * 
   * @param content - Content item to generate meta tags for
   * @param locale - Language locale for the content
   * @returns Complete meta tags object
   */
  async generateMetaTags(content: ContentItem, locale: string): Promise<MetaTags> {
    const url = `${this.baseUrl}${content.url}`
    const image = content.coverImage || this.defaultImage
    const author = content.author || this.defaultAuthor
    
    // Generate structured data
    const structuredData = await this.generateStructuredData(content)

    return {
      // Basic meta tags
      title: content.title,
      description: content.description || `${content.title} - ${this.siteName}`,
      keywords: content.tags?.join(', '),
      canonical: url,
      robots: 'index, follow',
      viewport: 'width=device-width, initial-scale=1',

      // Open Graph tags
      openGraph: {
        'og:title': content.title,
        'og:description': content.description || `${content.title} - ${this.siteName}`,
        'og:type': this.getOpenGraphType(content),
        'og:url': url,
        'og:image': image,
        'og:image:alt': content.title,
        'og:site_name': this.siteName,
        'og:locale': locale,
        'og:locale:alternate': locale === 'en' ? ['zh'] : ['en'],
        ...(content.author && { 'og:article:author': author }),
        ...(content.publishedAt && { 'og:article:published_time': content.publishedAt }),
        ...(content.tags && { 'og:article:tag': content.tags })
      },

      // Twitter Card tags
      twitter: {
        'twitter:card': image ? 'summary_large_image' : 'summary',
        'twitter:title': content.title,
        'twitter:description': content.description || `${content.title} - ${this.siteName}`,
        ...(image && { 
          'twitter:image': image,
          'twitter:image:alt': content.title 
        })
      },

      // Structured data
      structuredData
    }
  }

  /**
   * Generate JSON-LD structured data
   * 
   * Creates appropriate structured data based on content type
   * following Schema.org vocabulary for better search engine
   * understanding and rich snippets.
   * 
   * @param content - Content item to generate structured data for
   * @returns JSON-LD structured data object
   */
  async generateStructuredData(content: ContentItem): Promise<StructuredData> {
    const baseData = {
      '@context': 'https://schema.org' as const,
      name: content.title,
      description: content.description,
      url: `${this.baseUrl}${content.url}`,
      image: content.coverImage,
      datePublished: content.publishedAt,
      dateModified: content.createdAt
    }

    // Determine content type from URL pattern
    const contentType = this.getContentTypeFromUrl(content.url)

    switch (contentType) {
      case 'blog':
        return {
          ...baseData,
          '@type': 'BlogPosting',
          headline: content.title,
          author: {
            '@type': 'Person',
            name: content.author || this.defaultAuthor,
            ...(content.authorImage && { image: content.authorImage })
          },
          publisher: {
            '@type': 'Organization',
            name: this.siteName,
            url: this.baseUrl
          },
          mainEntityOfPage: {
            '@type': 'WebPage',
            '@id': `${this.baseUrl}${content.url}`
          },
          keywords: content.tags?.join(', '),
          wordCount: this.estimateWordCount(content.body?.raw || ''),
          timeRequired: this.estimateReadingTime(content.body?.raw || '')
        } as BlogPostingStructuredData

      case 'product':
        return {
          ...baseData,
          '@type': 'SoftwareApplication',
          applicationCategory: 'BusinessApplication',
          operatingSystem: 'Web Browser'
        } as ProductStructuredData

      case 'case-study':
      default:
        return {
          ...baseData,
          '@type': 'Article',
          headline: content.title,
          author: {
            '@type': 'Person',
            name: content.author || this.defaultAuthor,
            ...(content.authorImage && { image: content.authorImage })
          },
          publisher: {
            '@type': 'Organization',
            name: this.siteName,
            url: this.baseUrl
          },
          mainEntityOfPage: {
            '@type': 'WebPage',
            '@id': `${this.baseUrl}${content.url}`
          }
        } as ArticleStructuredData
    }
  }

  /**
   * Generate XML sitemap
   * 
   * Creates a comprehensive XML sitemap with proper hreflang
   * attributes for multi-language SEO and search engine indexing.
   * 
   * @param content - Array of all content items
   * @param config - Sitemap configuration options
   * @returns XML sitemap string
   */
  async generateSitemap(content: ContentItem[], config: SitemapConfig): Promise<string> {
    const urls: string[] = []

    // Add static pages
    for (const page of config.staticPages) {
      urls.push(this.generateSitemapEntry({
        url: `${config.baseUrl}${page.path}`,
        changefreq: page.changefreq || config.defaultChangefreq,
        priority: page.priority || config.defaultPriority,
        lastmod: new Date().toISOString().split('T')[0]
      }))
    }

    // Group content by slug for alternate language handling
    const contentBySlug = this.groupContentBySlug(content)

    // Add content pages
    for (const [slug, items] of contentBySlug.entries()) {
      for (const item of items) {
        const alternates = config.includeAlternates 
          ? items.filter(alt => alt.lang !== item.lang).map(alt => ({
              hreflang: alt.lang,
              href: `${config.baseUrl}${alt.url}`
            }))
          : undefined

        urls.push(this.generateSitemapEntry({
          url: `${config.baseUrl}${item.url}`,
          lastmod: item.publishedAt || item.createdAt,
          changefreq: config.defaultChangefreq,
          priority: item.featured ? 0.8 : 0.6,
          alternates
        }))
      }
    }

    return this.wrapSitemapXML(urls.join('\n'))
  }

  /**
   * Generate RSS feed
   * 
   * Creates RSS 2.0 compliant feed for content syndication
   * with proper encoding and metadata for feed readers.
   * 
   * @param content - Array of content items to include
   * @param config - RSS configuration options
   * @param locale - Language locale for the feed
   * @returns RSS XML string
   */
  async generateRSSFeed(
    content: ContentItem[], 
    config: RSSConfig, 
    locale: string
  ): Promise<string> {
    // Filter and sort content
    const feedContent = content
      .filter(item => item.lang === locale)
      .sort((a, b) => {
        const dateA = new Date(a.publishedAt || a.createdAt)
        const dateB = new Date(b.publishedAt || b.createdAt)
        return dateB.getTime() - dateA.getTime()
      })
      .slice(0, config.maxItems)

    const items = feedContent.map(item => this.generateRSSItem(item, config)).join('\n')

    return `<?xml version="1.0" encoding="UTF-8"?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
  <channel>
    <title>${this.escapeXML(config.title)}</title>
    <description>${this.escapeXML(config.description)}</description>
    <link>${config.baseUrl}</link>
    <language>${config.language}</language>
    <lastBuildDate>${new Date().toUTCString()}</lastBuildDate>
    <generator>ShipAny CMS</generator>
    <atom:link href="${config.baseUrl}/rss${locale === 'zh' ? '-zh' : ''}.xml" rel="self" type="application/rss+xml"/>
    ${items}
  </channel>
</rss>`
  }

  /**
   * Generate all SEO data in bulk
   * 
   * Convenience method that generates sitemap, RSS feeds, and
   * structured data for all content in a single operation.
   * 
   * @param locale - Optional locale filter
   * @returns Object containing all generated SEO data
   */
  async generateAllSEOData(locale?: string): Promise<{
    sitemap: string
    rssFeeds: { [locale: string]: string }
    structuredData: { [contentId: string]: StructuredData }
  }> {
    // This method would need to be implemented with actual content fetching
    // For now, return empty structure
    return {
      sitemap: '',
      rssFeeds: {},
      structuredData: {}
    }
  }

  // Private helper methods

  private getOpenGraphType(content: ContentItem): 'website' | 'article' | 'product' {
    const contentType = this.getContentTypeFromUrl(content.url)
    switch (contentType) {
      case 'product': return 'product'
      case 'blog':
      case 'case-study': return 'article'
      default: return 'website'
    }
  }

  private getContentTypeFromUrl(url: string): ContentType {
    if (url.includes('/blogs/')) return 'blog'
    if (url.includes('/products/')) return 'product'
    if (url.includes('/case-studies/')) return 'case-study'
    return 'blog' // fallback
  }

  private estimateWordCount(text: string): number {
    return text.split(/\s+/).filter(word => word.length > 0).length
  }

  private estimateReadingTime(text: string): string {
    const wordsPerMinute = 200
    const wordCount = this.estimateWordCount(text)
    const minutes = Math.ceil(wordCount / wordsPerMinute)
    return `PT${minutes}M`
  }

  private groupContentBySlug(content: ContentItem[]): Map<string, ContentItem[]> {
    const grouped = new Map<string, ContentItem[]>()
    
    for (const item of content) {
      const key = `${this.getContentTypeFromUrl(item.url)}-${item.slug}`
      if (!grouped.has(key)) {
        grouped.set(key, [])
      }
      grouped.get(key)!.push(item)
    }
    
    return grouped
  }

  private generateSitemapEntry(entry: {
    url: string
    lastmod?: string
    changefreq: string
    priority: number
    alternates?: Array<{ hreflang: string; href: string }>
  }): string {
    const alternateLinks = entry.alternates?.map(alt => 
      `    <xhtml:link rel="alternate" hreflang="${alt.hreflang}" href="${alt.href}"/>`
    ).join('\n') || ''

    return `  <url>
    <loc>${entry.url}</loc>
    ${entry.lastmod ? `<lastmod>${entry.lastmod}</lastmod>` : ''}
    <changefreq>${entry.changefreq}</changefreq>
    <priority>${entry.priority}</priority>
${alternateLinks}
  </url>`
  }

  private wrapSitemapXML(content: string): string {
    return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:xhtml="http://www.w3.org/1999/xhtml">
${content}
</urlset>`
  }

  private generateRSSItem(item: ContentItem, config: RSSConfig): string {
    const pubDate = new Date(item.publishedAt || item.createdAt).toUTCString()
    const content = config.includeContent ?
      `<content:encoded><![CDATA[${item.body?.raw || ''}]]></content:encoded>` : ''

    return `    <item>
      <title>${this.escapeXML(item.title)}</title>
      <description>${this.escapeXML(item.description || '')}</description>
      <link>${this.baseUrl}${item.url}</link>
      <guid>${this.baseUrl}${item.url}</guid>
      <pubDate>${pubDate}</pubDate>
      ${item.author ? `<author>${this.escapeXML(item.author)}</author>` : ''}
      ${item.tags?.map(tag => `<category>${this.escapeXML(tag)}</category>`).join('\n      ') || ''}
      ${content}
    </item>`
  }

  private escapeXML(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;')
  }
}

// Export default instance
export const seoService = new SEOServiceImpl({
  baseUrl: process.env.NEXT_PUBLIC_SITE_URL || 'https://shipany.ai',
  siteName: 'ShipAny',
  defaultAuthor: 'ShipAny Team',
  defaultImage: '/images/og-default.jpg'
})
