import type * as A from "../../Collections/Immutable/Chunk/index.js";
import type * as O from "../../Option/index.js";
import type * as T from "../_internal/effect.js";
import type * as M from "../_internal/managed.js";
import { Stream } from "./definitions.js";
export declare function source<R, E, A>(managedSource: M.Managed<R, never, T.Effect<R, O.Option<E>, <PERSON><PERSON><A>>>): Stream<R, E, A>;
//# sourceMappingURL=source.d.ts.map