#!/bin/bash

# Script to fix environment variable access issues
# This script will replace direct process.env access with safe env utility

echo "Fixing environment variable access in all files..."

# Files to update
files=(
  "src/app/[locale]/(default)/products/page.tsx"
  "src/app/[locale]/(default)/blogs/[slug]/page.tsx"
  "src/app/[locale]/(default)/blogs/page.tsx"
  "src/app/[locale]/(default)/posts/[slug]/page.tsx"
  "src/app/[locale]/(default)/case-studies/[slug]/page.tsx"
  "src/app/[locale]/(default)/posts/page.tsx"
  "src/app/[locale]/(default)/case-studies/page.tsx"
)

for file in "${files[@]}"; do
  if [ -f "$file" ]; then
    echo "Processing $file..."
    
    # Add import if not present
    if ! grep -q "import { env } from" "$file"; then
      # Find the last import line and add our import after it
      sed -i '/^import.*from/a import { env } from "@/lib/env"' "$file"
    fi
    
    # Replace process.env access patterns
    sed -i 's/const webUrl = (typeof process !== '\''undefined'\'' && process\.env) ? process\.env\.NEXT_PUBLIC_WEB_URL : '\'''\'';//g' "$file"
    sed -i 's/let canonicalUrl = webUrl ? `\${webUrl}/let canonicalUrl = env.NEXT_PUBLIC_WEB_URL ? `\${env.NEXT_PUBLIC_WEB_URL}/g' "$file"
    sed -i 's/canonicalUrl = `\${webUrl}/canonicalUrl = `\${env.NEXT_PUBLIC_WEB_URL}/g' "$file"
    
    echo "Fixed $file"
  else
    echo "File not found: $file"
  fi
done

echo "Environment variable fixes completed!"
