# Development Dockerfile for Cloudflare Workers + Next.js (Ubuntu-based)
# This version uses Ubuntu to ensure full compatibility with Cloudflare Workerd
FROM node:20-bullseye

# Install system dependencies for development
RUN apt-get update && apt-get install -y \
    git \
    curl \
    bash \
    openssh-client \
    libc++1 \
    libunwind8 \
    && rm -rf /var/lib/apt/lists/* \
    && yarn global add pnpm

# Set working directory
WORKDIR /app

# Setup pnpm global directory and install wrangler
RUN mkdir -p /usr/local/bin && \
    export PNPM_HOME="/usr/local/bin" && \
    export PATH="$PNPM_HOME:$PATH" && \
    pnpm add -g wrangler@4.19.1

# Copy package files and configuration files needed for installation
COPY package.json pnpm-lock.yaml* ./
COPY contentlayer.config.ts ./
COPY next.config.mjs ./
COPY open-next.config.ts ./
COPY postcss.config.mjs ./
COPY source.config.ts ./
COPY tsconfig.json ./
COPY components.json ./

# Install dependencies (skip postinstall scripts in Docker build)
RUN pnpm install --ignore-scripts || (rm -f pnpm-lock.yaml && pnpm install --ignore-scripts)

# Create necessary directories
RUN mkdir -p .next .contentlayer .open-next

# Set up git configuration (for potential git operations)
RUN git config --global user.email "<EMAIL>" && \
    git config --global user.name "StarWind97"

# Expose ports
EXPOSE 3000 8787

# Set environment variables
ENV NODE_ENV=development
ENV SHELL=/bin/bash
ENV PNPM_HOME="/usr/local/bin"
ENV PATH="$PNPM_HOME:$PATH"

# Default command (will be overridden by docker-compose)
CMD ["tail", "-f", "/dev/null"]
